/**
 * Project upgrade analyzer using LLM integration
 * Analyzes project files to understand current state and configuration
 */

import { existsSync } from "fs";
import { join } from "path";

import { Agent, run } from "@openai/agents";

import type { FileAnalysisResult, PackageJson, ProjectAnalysisResult } from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

export class ProjectUpgradeAnalyzer {
  private fsManager: FileSystemManager;
  private agent?: Agent;

  constructor() {
    this.fsManager = new FileSystemManager();

    // Initialize the LLM agent for project analysis only if API key is available
    if (process.env.OPENAI_API_KEY) {
      this.agent = new Agent({
        name: "ProjectAnalyzer",
        instructions: `You are an expert TypeScript/React developer specializing in CSCS Agent projects.
          Your task is to analyze project files and provide detailed insights about their structure, dependencies, and configuration.

          When analyzing files, provide:
          1. A summary of the file's purpose and structure
          2. Key dependencies and their versions
          3. Configuration patterns and customizations
          4. Potential issues or outdated patterns
          5. Compatibility with the latest CSCS Agent framework

          Always return your analysis in a structured, detailed format that can be used for upgrade planning.
          Focus on identifying differences from standard templates and custom modifications that need to be preserved.`,
        model: process.env.OPENAI_MODEL || "gpt-4o",
      });
    }
  }

  /**
   * Analyze a complete project
   */
  async analyzeProject(projectPath: string): Promise<ProjectAnalysisResult> {
    Logger.info(`Analyzing project at: ${projectPath}`);

    // Analyze core files
    const packageJson = await this.analyzePackageJson(projectPath);
    const mainTsx = await this.analyzeMainTsx(projectPath);
    const agentConfig = await this.analyzeAgentConfig(projectPath);

    // Analyze additional configuration files
    const additionalFiles = await this.analyzeAdditionalFiles(projectPath);

    // Determine if this is a valid agent project
    const isValidAgentProject = this.validateAgentProject(packageJson, mainTsx, agentConfig);

    return {
      packageJson,
      mainTsx,
      agentConfig,
      additionalFiles,
      projectPath,
      isValidAgentProject,
    };
  }

  /**
   * Analyze package.json file
   */
  private async analyzePackageJson(projectPath: string): Promise<FileAnalysisResult> {
    const filePath = join(projectPath, "package.json");

    if (!existsSync(filePath)) {
      return {
        filePath,
        content: "",
        exists: false,
      };
    }

    const content = await this.fsManager.readFile(filePath);

    try {
      const packageData: PackageJson = JSON.parse(content);

      const analysis = await this.analyzeFileWithLLM(
        filePath,
        content,
        "This is a package.json file. Analyze the dependencies, scripts, and configuration for a CSCS Agent project.",
      );

      return {
        filePath,
        content,
        exists: true,
        analysis,
        dependencies: packageData.dependencies || {},
        devDependencies: packageData.devDependencies || {},
      };
    } catch (error) {
      Logger.warning(`Failed to parse package.json: ${error instanceof Error ? error.message : "Unknown error"}`);
      return {
        filePath,
        content,
        exists: true,
        analysis: "Failed to parse package.json - invalid JSON format",
      };
    }
  }

  /**
   * Analyze main.tsx file
   */
  private async analyzeMainTsx(projectPath: string): Promise<FileAnalysisResult> {
    const possiblePaths = [
      join(projectPath, "src", "main.tsx"),
      join(projectPath, "src", "main.ts"),
      join(projectPath, "src", "index.tsx"),
      join(projectPath, "src", "index.ts"),
    ];

    for (const filePath of possiblePaths) {
      if (existsSync(filePath)) {
        const content = await this.fsManager.readFile(filePath);
        const analysis = await this.analyzeFileWithLLM(
          filePath,
          content,
          "This is the main application entry point. Analyze the imports, router configuration, and app initialization for a CSCS Agent project.",
        );

        return {
          filePath,
          content,
          exists: true,
          analysis,
        };
      }
    }

    return {
      filePath: join(projectPath, "src", "main.tsx"),
      content: "",
      exists: false,
    };
  }

  /**
   * Analyze agent-config file
   */
  private async analyzeAgentConfig(projectPath: string): Promise<FileAnalysisResult> {
    const possiblePaths = [join(projectPath, "src", "agent-config.tsx"), join(projectPath, "src", "agent-config.ts")];

    for (const filePath of possiblePaths) {
      if (existsSync(filePath)) {
        const content = await this.fsManager.readFile(filePath);
        const analysis = await this.analyzeFileWithLLM(
          filePath,
          content,
          "This is an agent configuration file. Analyze the agent definitions, widgets, commands, and request configurations.",
        );

        return {
          filePath,
          content,
          exists: true,
          analysis,
        };
      }
    }

    return {
      filePath: join(projectPath, "src", "agent-config.tsx"),
      content: "",
      exists: false,
    };
  }

  /**
   * Analyze additional configuration files
   */
  private async analyzeAdditionalFiles(projectPath: string): Promise<FileAnalysisResult[]> {
    const additionalFiles: string[] = [
      "vite.config.js",
      "vite.config.ts",
      "tsconfig.json",
      "eslint.config.js",
      "tailwind.config.js",
      "tailwind.config.ts",
    ];

    const results: FileAnalysisResult[] = [];

    for (const fileName of additionalFiles) {
      const filePath = join(projectPath, fileName);

      if (existsSync(filePath)) {
        const content = await this.fsManager.readFile(filePath);
        const analysis = await this.analyzeFileWithLLM(
          filePath,
          content,
          `This is a ${fileName} configuration file. Analyze the configuration for compatibility with CSCS Agent projects.`,
        );

        results.push({
          filePath,
          content,
          exists: true,
          analysis,
        });
      }
    }

    return results;
  }

  /**
   * Analyze a file using LLM or fallback to basic analysis
   */
  private async analyzeFileWithLLM(filePath: string, content: string, context: string): Promise<string> {
    // If no agent is available, provide basic analysis
    if (!this.agent) {
      return this.provideFallbackAnalysis(filePath, content, context);
    }

    try {
      const prompt = `${context}

File: ${filePath}
Content:
\`\`\`
${content}
\`\`\`

Please provide a detailed analysis including:
1. Purpose and structure
2. Key dependencies and configurations
3. Custom modifications or non-standard patterns
4. Potential compatibility issues
5. Recommendations for upgrades

Keep your analysis concise but comprehensive.`;

      const result = await run(this.agent, prompt);

      if (!result.finalOutput || typeof result.finalOutput !== "string") {
        return "LLM analysis failed - no valid response received";
      }

      return result.finalOutput;
    } catch (error) {
      Logger.warning(
        `LLM analysis failed for ${filePath}: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      return this.provideFallbackAnalysis(filePath, content, context);
    }
  }

  /**
   * Provide basic fallback analysis when LLM is not available
   */
  private provideFallbackAnalysis(filePath: string, content: string, context: string): string {
    const fileName = filePath.split(/[/\\]/).pop() || "file";
    const lines = content.split("\n").length;
    const size = content.length;

    let analysis = `Basic analysis for ${fileName}:\n`;
    analysis += `- File size: ${size} characters, ${lines} lines\n`;
    analysis += `- Context: ${context}\n`;

    // Basic pattern detection
    if (fileName === "package.json") {
      try {
        const pkg = JSON.parse(content);
        analysis += `- Package name: ${pkg.name || "unknown"}\n`;
        analysis += `- Version: ${pkg.version || "unknown"}\n`;
        analysis += `- Dependencies: ${Object.keys(pkg.dependencies || {}).length}\n`;
        analysis += `- Dev dependencies: ${Object.keys(pkg.devDependencies || {}).length}\n`;
      } catch {
        analysis += "- Invalid JSON format\n";
      }
    } else if (fileName.endsWith(".tsx") || fileName.endsWith(".ts")) {
      const imports = content.match(/import .* from .*/g) || [];
      analysis += `- Import statements: ${imports.length}\n`;
      analysis += `- Contains React: ${content.includes("React") ? "yes" : "no"}\n`;
      analysis += `- Contains CSCS Agent: ${content.includes("@cscs-agent") ? "yes" : "no"}\n`;
    }

    analysis +=
      "\nNote: Limited analysis available without OpenAI API key. For detailed analysis, set OPENAI_API_KEY environment variable.";

    return analysis;
  }

  /**
   * Validate if this is a valid agent project
   */
  private validateAgentProject(
    packageJson: FileAnalysisResult,
    mainTsx: FileAnalysisResult,
    agentConfig: FileAnalysisResult,
  ): boolean {
    // Check if package.json exists and has CSCS Agent dependencies
    if (!packageJson.exists || !packageJson.dependencies) {
      return false;
    }

    const hasCorePackage =
      Object.keys(packageJson.dependencies).some((dep) => dep.includes("@cscs-agent/core")) ||
      Object.keys(packageJson.devDependencies || {}).some((dep) => dep.includes("@cscs-agent/core"));

    // Check if main entry point exists
    if (!mainTsx.exists) {
      return false;
    }

    // Check if main.tsx imports from @cscs-agent/core
    const hasAgentImports = mainTsx.content.includes("@cscs-agent/core");

    // Agent config is optional but recommended
    const hasAgentConfig = agentConfig.exists;

    return hasCorePackage && hasAgentImports;
  }
}
